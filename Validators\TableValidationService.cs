using DynamicTableAPI.Models;

namespace DynamicTableAPI.Validators
{
    /// <summary>
    /// Service for validating table and column definitions
    /// </summary>
    public class TableValidationService : ITableValidationService
    {
        private static readonly HashSet<string> SqlServerReservedWords = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            "ADD", "ALL", "ALTER", "AND", "ANY", "AS", "ASC", "AUTHORIZATION", "BAC<PERSON><PERSON>", "BEGIN", "BETWEEN", "BREAK", "BROWSE", "BULK", "BY", "CASCADE", "CASE", "CHECK", "CHECKPOINT", "CLOSE", "CLUSTERED", "COALESCE", "COLLATE", "COLUMN", "COMMIT", "COMPUTE", "CONSTRAINT", "CONTAINS", "CONTAINSTABLE", "CONTINUE", "CONVERT", "CREATE", "CROSS", "CURRENT", "CURRENT_DATE", "CURRENT_TIME", "CURRENT_TIMESTAMP", "CURRENT_USER", "CURSOR", "DATABASE", "DBCC", "DEALLOCATE", "DECLARE", "DEFAULT", "DELETE", "DENY", "DESC", "DISK", "DISTINCT", "DISTRIBUTED", "DOUBLE", "DROP", "DUMP", "ELSE", "END", "ERRLVL", "ESCAPE", "EXCEPT", "EXEC", "EXECUTE", "EXISTS", "EXIT", "EXTERNAL", "FETCH", "FILE", "FILLFACTOR", "FOR", "FOREIGN", "FREETEXT", "FREETEXTTABLE", "FROM", "FULL", "FUNCTION", "GOTO", "GRANT", "GROUP", "HAVING", "HOLDLOCK", "IDENTITY", "IDENTITY_INSERT", "IDENTITYCOL", "IF", "IN", "INDEX", "INNER", "INSERT", "INTERSECT", "INTO", "IS", "JOIN", "KEY", "KILL", "LEFT", "LIKE", "LINENO", "LOAD", "MERGE", "NATIONAL", "NOCHECK", "NONCLUSTERED", "NOT", "NULL", "NULLIF", "OF", "OFF", "OFFSETS", "ON", "OPEN", "OPENDATASOURCE", "OPENQUERY", "OPENROWSET", "OPENXML", "OPTION", "OR", "ORDER", "OUTER", "OVER", "PERCENT", "PIVOT", "PLAN", "PRECISION", "PRIMARY", "PRINT", "PROC", "PROCEDURE", "PUBLIC", "RAISERROR", "READ", "READTEXT", "RECONFIGURE", "REFERENCES", "REPLICATION", "RESTORE", "RESTRICT", "RETURN", "REVERT", "REVOKE", "RIGHT", "ROLLBACK", "ROWCOUNT", "ROWGUIDCOL", "RULE", "SAVE", "SCHEMA", "SECURITYAUDIT", "SELECT", "SEMANTICKEYPHRASETABLE", "SEMANTICSIMILARITYDETAILSTABLE", "SEMANTICSIMILARITYTABLE", "SESSION_USER", "SET", "SETUSER", "SHUTDOWN", "SOME", "STATISTICS", "SYSTEM_USER", "TABLE", "TABLESAMPLE", "TEXTSIZE", "THEN", "TO", "TOP", "TRAN", "TRANSACTION", "TRIGGER", "TRUNCATE", "TRY_CONVERT", "TSEQUAL", "UNION", "UNIQUE", "UNPIVOT", "UPDATE", "UPDATETEXT", "USE", "USER", "VALUES", "VARYING", "VIEW", "WAITFOR", "WHEN", "WHERE", "WHILE", "WITH", "WITHIN GROUP", "WRITETEXT"
        };

        private static readonly HashSet<string> SupportedDataTypes = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            "string", "int", "datetime", "bool", "decimal", "guid"
        };

        /// <summary>
        /// Validates a table creation request
        /// </summary>
        /// <param name="request">The table creation request to validate</param>
        /// <returns>Validation result with any errors</returns>
        public ValidationResult ValidateCreateTableRequest(CreateTableRequest request)
        {
            var errors = new List<string>();

            // Validate table name
            var tableNameValidation = ValidateTableName(request.TableName);
            if (!tableNameValidation.IsValid)
            {
                errors.AddRange(tableNameValidation.Errors);
            }

            // Validate columns
            if (request.Columns == null || !request.Columns.Any())
            {
                errors.Add("At least one column definition is required");
            }
            else
            {
                var columnNames = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
                
                for (int i = 0; i < request.Columns.Count; i++)
                {
                    var column = request.Columns[i];
                    var columnValidation = ValidateColumnDefinition(column, i);
                    
                    if (!columnValidation.IsValid)
                    {
                        errors.AddRange(columnValidation.Errors);
                    }

                    // Check for duplicate column names
                    if (!string.IsNullOrEmpty(column.Name))
                    {
                        if (columnNames.Contains(column.Name))
                        {
                            errors.Add($"Duplicate column name '{column.Name}' at index {i}");
                        }
                        else
                        {
                            columnNames.Add(column.Name);
                        }
                    }
                }
            }

            return new ValidationResult
            {
                IsValid = !errors.Any(),
                Errors = errors
            };
        }

        /// <summary>
        /// Validates a table name
        /// </summary>
        /// <param name="tableName">The table name to validate</param>
        /// <returns>Validation result</returns>
        public ValidationResult ValidateTableName(string tableName)
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(tableName))
            {
                errors.Add("Table name is required");
                return new ValidationResult { IsValid = false, Errors = errors };
            }

            // Check length
            if (tableName.Length > 128)
            {
                errors.Add("Table name cannot exceed 128 characters");
            }

            // Check for valid identifier format
            if (!System.Text.RegularExpressions.Regex.IsMatch(tableName, @"^[a-zA-Z][a-zA-Z0-9_]*$"))
            {
                errors.Add("Table name must start with a letter and contain only letters, numbers, and underscores");
            }

            // Check for reserved words
            if (SqlServerReservedWords.Contains(tableName))
            {
                errors.Add($"'{tableName}' is a SQL Server reserved word and cannot be used as a table name");
            }

            return new ValidationResult
            {
                IsValid = !errors.Any(),
                Errors = errors
            };
        }

        /// <summary>
        /// Validates a column definition
        /// </summary>
        /// <param name="column">The column definition to validate</param>
        /// <param name="index">The index of the column in the request (for error reporting)</param>
        /// <returns>Validation result</returns>
        public ValidationResult ValidateColumnDefinition(ColumnDefinition column, int index)
        {
            var errors = new List<string>();

            // Validate column name
            if (string.IsNullOrWhiteSpace(column.Name))
            {
                errors.Add($"Column name is required at index {index}");
            }
            else
            {
                if (column.Name.Length > 128)
                {
                    errors.Add($"Column name at index {index} cannot exceed 128 characters");
                }

                if (!System.Text.RegularExpressions.Regex.IsMatch(column.Name, @"^[a-zA-Z][a-zA-Z0-9_]*$"))
                {
                    errors.Add($"Column name '{column.Name}' at index {index} must start with a letter and contain only letters, numbers, and underscores");
                }

                if (SqlServerReservedWords.Contains(column.Name))
                {
                    errors.Add($"'{column.Name}' at index {index} is a SQL Server reserved word and cannot be used as a column name");
                }

                // Check for default column names that are automatically added
                if (string.Equals(column.Name, "Id", StringComparison.OrdinalIgnoreCase) ||
                    string.Equals(column.Name, "CreatedDate", StringComparison.OrdinalIgnoreCase) ||
                    string.Equals(column.Name, "UpdatedDate", StringComparison.OrdinalIgnoreCase))
                {
                    errors.Add($"Column name '{column.Name}' at index {index} is reserved for system columns");
                }
            }

            // Validate data type
            if (string.IsNullOrWhiteSpace(column.Type))
            {
                errors.Add($"Column type is required at index {index}");
            }
            else if (!SupportedDataTypes.Contains(column.Type))
            {
                errors.Add($"Unsupported data type '{column.Type}' at index {index}. Supported types: {string.Join(", ", SupportedDataTypes)}");
            }
            else
            {
                // Type-specific validations
                switch (column.Type.ToLower())
                {
                    case "string":
                        if (column.MaxLength.HasValue && (column.MaxLength.Value < 1 || column.MaxLength.Value > 4000))
                        {
                            errors.Add($"String column '{column.Name}' at index {index} max length must be between 1 and 4000");
                        }
                        break;

                    case "decimal":
                        if (column.Precision.HasValue && (column.Precision.Value < 1 || column.Precision.Value > 38))
                        {
                            errors.Add($"Decimal column '{column.Name}' at index {index} precision must be between 1 and 38");
                        }
                        if (column.Scale.HasValue && column.Precision.HasValue && column.Scale.Value > column.Precision.Value)
                        {
                            errors.Add($"Decimal column '{column.Name}' at index {index} scale cannot be greater than precision");
                        }
                        if (column.Scale.HasValue && (column.Scale.Value < 0 || column.Scale.Value > 38))
                        {
                            errors.Add($"Decimal column '{column.Name}' at index {index} scale must be between 0 and 38");
                        }
                        break;
                }
            }

            return new ValidationResult
            {
                IsValid = !errors.Any(),
                Errors = errors
            };
        }
    }

    /// <summary>
    /// Interface for table validation service
    /// </summary>
    public interface ITableValidationService
    {
        ValidationResult ValidateCreateTableRequest(CreateTableRequest request);
        ValidationResult ValidateTableName(string tableName);
        ValidationResult ValidateColumnDefinition(ColumnDefinition column, int index);
    }

    /// <summary>
    /// Represents the result of a validation operation
    /// </summary>
    public class ValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
    }
}
