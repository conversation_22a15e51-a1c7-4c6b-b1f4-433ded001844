using DynamicTableAPI.Models;
using System.Net;
using System.Text.Json;

namespace DynamicTableAPI.Middleware
{
    /// <summary>
    /// Global exception handling middleware
    /// </summary>
    public class GlobalExceptionMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<GlobalExceptionMiddleware> _logger;

        public GlobalExceptionMiddleware(RequestDelegate next, ILogger<GlobalExceptionMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            try
            {
                await _next(context);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An unhandled exception occurred");
                await HandleExceptionAsync(context, ex);
            }
        }

        private static async Task HandleExceptionAsync(HttpContext context, Exception exception)
        {
            context.Response.ContentType = "application/json";

            var response = new ApiResponse<object>();

            switch (exception)
            {
                case ArgumentNullException nullEx:
                    response.Success = false;
                    response.Message = "Required parameter is missing";
                    context.Response.StatusCode = (int)HttpStatusCode.BadRequest;
                    break;

                case ArgumentException argEx:
                    response.Success = false;
                    response.Message = argEx.Message;
                    context.Response.StatusCode = (int)HttpStatusCode.BadRequest;
                    break;

                case InvalidOperationException invOpEx when invOpEx.Message.Contains("already exists"):
                    response.Success = false;
                    response.Message = invOpEx.Message;
                    context.Response.StatusCode = (int)HttpStatusCode.Conflict;
                    break;

                case InvalidOperationException invOpEx when invOpEx.Message.Contains("does not exist"):
                    response.Success = false;
                    response.Message = invOpEx.Message;
                    context.Response.StatusCode = (int)HttpStatusCode.NotFound;
                    break;

                case InvalidOperationException invOpEx:
                    response.Success = false;
                    response.Message = invOpEx.Message;
                    context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                    break;

                case UnauthorizedAccessException:
                    response.Success = false;
                    response.Message = "Unauthorized access";
                    context.Response.StatusCode = (int)HttpStatusCode.Unauthorized;
                    break;

                case NotImplementedException:
                    response.Success = false;
                    response.Message = "Feature not implemented";
                    context.Response.StatusCode = (int)HttpStatusCode.NotImplemented;
                    break;

                case TimeoutException:
                    response.Success = false;
                    response.Message = "Request timeout";
                    context.Response.StatusCode = (int)HttpStatusCode.RequestTimeout;
                    break;

                default:
                    response.Success = false;
                    response.Message = "An internal server error occurred";
                    context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                    break;
            }

            var jsonResponse = JsonSerializer.Serialize(response, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = true
            });

            await context.Response.WriteAsync(jsonResponse);
        }
    }

    /// <summary>
    /// Extension method to register the global exception middleware
    /// </summary>
    public static class GlobalExceptionMiddlewareExtensions
    {
        public static IApplicationBuilder UseGlobalExceptionHandling(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<GlobalExceptionMiddleware>();
        }
    }
}
