using Microsoft.EntityFrameworkCore;

namespace DynamicTableAPI.Data
{
    /// <summary>
    /// Repository implementation for table operations
    /// </summary>
    public class TableRepository : ITableRepository
    {
        private readonly DynamicTableDbContext _context;
        private readonly ILogger<TableRepository> _logger;

        public TableRepository(DynamicTableDbContext context, ILogger<TableRepository> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// Checks if a table exists in the database
        /// </summary>
        /// <param name="tableName">Name of the table to check</param>
        /// <returns>True if table exists, false otherwise</returns>
        public async Task<bool> TableExistsAsync(string tableName)
        {
            try
            {
                return await _context.TableExistsAsync(tableName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if table exists: {TableName}", tableName);
                throw;
            }
        }

        /// <summary>
        /// Gets list of all user tables in the database
        /// </summary>
        /// <returns>List of table names</returns>
        public async Task<List<string>> GetUserTablesAsync()
        {
            try
            {
                return await _context.GetUserTablesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user tables");
                throw;
            }
        }

        /// <summary>
        /// Gets table schema information
        /// </summary>
        /// <param name="tableName">Name of the table</param>
        /// <returns>Table schema information as list of dictionaries</returns>
        public async Task<List<Dictionary<string, object?>>> GetTableSchemaAsync(string tableName)
        {
            try
            {
                return await _context.GetTableSchemaAsync(tableName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting table schema: {TableName}", tableName);
                throw;
            }
        }

        /// <summary>
        /// Executes a raw SQL command
        /// </summary>
        /// <param name="sql">SQL command to execute</param>
        /// <returns>Number of rows affected</returns>
        public async Task<int> ExecuteRawSqlAsync(string sql)
        {
            try
            {
                _logger.LogDebug("Executing SQL command: {Sql}", sql);
                return await _context.ExecuteRawSqlAsync(sql);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing SQL command: {Sql}", sql);
                throw;
            }
        }

        /// <summary>
        /// Executes a raw SQL query and returns results
        /// </summary>
        /// <param name="sql">SQL query to execute</param>
        /// <returns>Query results as list of dictionaries</returns>
        public async Task<List<Dictionary<string, object?>>> ExecuteRawQueryAsync(string sql)
        {
            try
            {
                _logger.LogDebug("Executing SQL query: {Sql}", sql);
                return await _context.ExecuteRawQueryAsync(sql);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing SQL query: {Sql}", sql);
                throw;
            }
        }

        /// <summary>
        /// Gets the total count of records in a table
        /// </summary>
        /// <param name="tableName">Name of the table</param>
        /// <returns>Total number of records</returns>
        public async Task<int> GetTableRecordCountAsync(string tableName)
        {
            try
            {
                var sql = $"SELECT COUNT(*) FROM [{tableName}]";
                var result = await ExecuteRawQueryAsync(sql);
                return Convert.ToInt32(result.First().Values.First());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting record count for table: {TableName}", tableName);
                throw;
            }
        }

        /// <summary>
        /// Deletes all data from a table
        /// </summary>
        /// <param name="tableName">Name of the table</param>
        /// <returns>Number of rows deleted</returns>
        public async Task<int> TruncateTableAsync(string tableName)
        {
            try
            {
                // Get count before truncation
                var countBefore = await GetTableRecordCountAsync(tableName);
                
                // TRUNCATE is faster but doesn't work with foreign keys, so use DELETE
                var sql = $"DELETE FROM [{tableName}]";
                await ExecuteRawSqlAsync(sql);
                
                _logger.LogInformation("Truncated table {TableName}, deleted {RecordCount} records", tableName, countBefore);
                return countBefore;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error truncating table: {TableName}", tableName);
                throw;
            }
        }

        /// <summary>
        /// Drops a table from the database
        /// </summary>
        /// <param name="tableName">Name of the table to drop</param>
        /// <returns>True if successful</returns>
        public async Task<bool> DropTableAsync(string tableName)
        {
            try
            {
                if (!await TableExistsAsync(tableName))
                {
                    _logger.LogWarning("Attempted to drop non-existent table: {TableName}", tableName);
                    return false;
                }

                var sql = $"DROP TABLE [{tableName}]";
                await ExecuteRawSqlAsync(sql);
                
                _logger.LogInformation("Successfully dropped table: {TableName}", tableName);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error dropping table: {TableName}", tableName);
                throw;
            }
        }
    }
}
