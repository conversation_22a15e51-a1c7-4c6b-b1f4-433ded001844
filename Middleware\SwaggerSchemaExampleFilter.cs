using DynamicTableAPI.Models;
using Microsoft.OpenApi.Any;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace DynamicTableAPI.Middleware
{
    /// <summary>
    /// Swagger schema filter to add examples to API documentation
    /// </summary>
    public class SwaggerSchemaExampleFilter : ISchemaFilter
    {
        public void Apply(OpenApiSchema schema, SchemaFilterContext context)
        {
            if (context.Type == typeof(CreateTableRequest))
            {
                schema.Example = new OpenApiObject
                {
                    ["tableName"] = new OpenApiString("Products"),
                    ["columns"] = new OpenApiArray
                    {
                        new OpenApiObject
                        {
                            ["name"] = new OpenApiString("Name"),
                            ["type"] = new OpenApiString("string"),
                            ["maxLength"] = new OpenApiInteger(100),
                            ["isRequired"] = new OpenApiBoolean(true)
                        },
                        new OpenApiObject
                        {
                            ["name"] = new OpenApiString("Price"),
                            ["type"] = new OpenApiString("decimal"),
                            ["precision"] = new OpenApiInteger(10),
                            ["scale"] = new OpenApiInteger(2),
                            ["isRequired"] = new OpenApiBoolean(true)
                        },
                        new OpenApiObject
                        {
                            ["name"] = new OpenApiString("IsActive"),
                            ["type"] = new OpenApiString("bool"),
                            ["defaultValue"] = new OpenApiBoolean(true)
                        },
                        new OpenApiObject
                        {
                            ["name"] = new OpenApiString("Description"),
                            ["type"] = new OpenApiString("string"),
                            ["maxLength"] = new OpenApiInteger(500),
                            ["isRequired"] = new OpenApiBoolean(false)
                        },
                        new OpenApiObject
                        {
                            ["name"] = new OpenApiString("CategoryId"),
                            ["type"] = new OpenApiString("int"),
                            ["isRequired"] = new OpenApiBoolean(false)
                        },
                        new OpenApiObject
                        {
                            ["name"] = new OpenApiString("ProductId"),
                            ["type"] = new OpenApiString("guid"),
                            ["isRequired"] = new OpenApiBoolean(true)
                        }
                    }
                };
            }
            else if (context.Type == typeof(InsertDataRequest))
            {
                schema.Example = new OpenApiObject
                {
                    ["data"] = new OpenApiObject
                    {
                        ["Name"] = new OpenApiString("Sample Product"),
                        ["Price"] = new OpenApiDouble(29.99),
                        ["IsActive"] = new OpenApiBoolean(true),
                        ["Description"] = new OpenApiString("This is a sample product description"),
                        ["CategoryId"] = new OpenApiInteger(1),
                        ["ProductId"] = new OpenApiString("123e4567-e89b-12d3-a456-************")
                    }
                };
            }
            else if (context.Type == typeof(ColumnDefinition))
            {
                schema.Example = new OpenApiObject
                {
                    ["name"] = new OpenApiString("SampleColumn"),
                    ["type"] = new OpenApiString("string"),
                    ["maxLength"] = new OpenApiInteger(255),
                    ["isRequired"] = new OpenApiBoolean(false),
                    ["defaultValue"] = new OpenApiString("Default Value")
                };
            }
        }
    }
}
