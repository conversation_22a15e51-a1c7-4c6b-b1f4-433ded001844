using System.ComponentModel.DataAnnotations;

namespace DynamicTableAPI.Models
{
    /// <summary>
    /// Request model for inserting data into a dynamic table
    /// </summary>
    public class InsertDataRequest
    {
        /// <summary>
        /// Data to insert as key-value pairs where key is column name and value is the data
        /// </summary>
        [Required(ErrorMessage = "Data is required")]
        public Dictionary<string, object?> Data { get; set; } = new Dictionary<string, object?>();
    }

    /// <summary>
    /// Request model for inserting multiple rows of data
    /// </summary>
    public class BulkInsertDataRequest
    {
        /// <summary>
        /// List of data rows to insert
        /// </summary>
        [Required(ErrorMessage = "Data is required")]
        [MinLength(1, ErrorMessage = "At least one data row is required")]
        public List<Dictionary<string, object?>> Data { get; set; } = new List<Dictionary<string, object?>>();
    }
}
