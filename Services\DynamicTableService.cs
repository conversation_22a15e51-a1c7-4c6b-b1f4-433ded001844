using DynamicTableAPI.Data;
using DynamicTableAPI.Models;
using DynamicTableAPI.Validators;
using System.Text;

namespace DynamicTableAPI.Services
{
    /// <summary>
    /// Service for managing dynamic table operations
    /// </summary>
    public class DynamicTableService : IDynamicTableService
    {
        private readonly DynamicTableDbContext _context;
        private readonly ITableValidationService _validationService;
        private readonly ILogger<DynamicTableService> _logger;

        public DynamicTableService(
            DynamicTableDbContext context,
            ITableValidationService validationService,
            ILogger<DynamicTableService> logger)
        {
            _context = context;
            _validationService = validationService;
            _logger = logger;
        }

        /// <summary>
        /// Creates a new dynamic table with the specified columns
        /// </summary>
        /// <param name="request">Table creation request</param>
        /// <returns>Created table schema</returns>
        public async Task<TableSchema> CreateTableAsync(CreateTableRequest request)
        {
            _logger.LogInformation("Creating table: {TableName}", request.TableName);

            // Validate the request
            var validationResult = _validationService.ValidateCreateTableRequest(request);
            if (!validationResult.IsValid)
            {
                throw new ArgumentException($"Validation failed: {string.Join(", ", validationResult.Errors)}");
            }

            // Check if table already exists
            if (await _context.TableExistsAsync(request.TableName))
            {
                throw new InvalidOperationException($"Table '{request.TableName}' already exists");
            }

            // Generate CREATE TABLE SQL
            var createTableSql = GenerateCreateTableSql(request);
            
            try
            {
                // Execute the CREATE TABLE command
                await _context.ExecuteRawSqlAsync(createTableSql);
                
                _logger.LogInformation("Successfully created table: {TableName}", request.TableName);

                // Return the created table schema
                return await GetTableSchemaAsync(request.TableName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create table: {TableName}", request.TableName);
                throw new InvalidOperationException($"Failed to create table '{request.TableName}': {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Gets the schema information for a specific table
        /// </summary>
        /// <param name="tableName">Name of the table</param>
        /// <returns>Table schema information</returns>
        public async Task<TableSchema> GetTableSchemaAsync(string tableName)
        {
            _logger.LogInformation("Getting schema for table: {TableName}", tableName);

            if (!await _context.TableExistsAsync(tableName))
            {
                throw new ArgumentException($"Table '{tableName}' does not exist");
            }

            var schemaData = await _context.GetTableSchemaAsync(tableName);
            
            var tableSchema = new TableSchema
            {
                TableName = tableName,
                CreatedDate = DateTime.UtcNow, // This would ideally come from table metadata
                Columns = schemaData.Select(row => new ColumnSchema
                {
                    ColumnName = row["ColumnName"]?.ToString() ?? "",
                    DataType = row["DataType"]?.ToString() ?? "",
                    IsNullable = string.Equals(row["IsNullable"]?.ToString(), "YES", StringComparison.OrdinalIgnoreCase),
                    MaxLength = row["MaxLength"] as int?,
                    Precision = row["Precision"] as int?,
                    Scale = row["Scale"] as int?,
                    DefaultValue = row["DefaultValue"]?.ToString(),
                    IsPrimaryKey = Convert.ToBoolean(row["IsPrimaryKey"])
                }).ToList()
            };

            return tableSchema;
        }

        /// <summary>
        /// Gets a list of all user tables in the database
        /// </summary>
        /// <returns>List of table names</returns>
        public async Task<List<string>> GetTablesAsync()
        {
            _logger.LogInformation("Getting list of all tables");
            return await _context.GetUserTablesAsync();
        }

        /// <summary>
        /// Inserts data into a dynamic table
        /// </summary>
        /// <param name="tableName">Name of the table</param>
        /// <param name="data">Data to insert</param>
        /// <returns>Number of rows affected</returns>
        public async Task<int> InsertDataAsync(string tableName, Dictionary<string, object?> data)
        {
            _logger.LogInformation("Inserting data into table: {TableName}", tableName);

            if (!await _context.TableExistsAsync(tableName))
            {
                throw new ArgumentException($"Table '{tableName}' does not exist");
            }

            if (data == null || !data.Any())
            {
                throw new ArgumentException("Data is required for insertion");
            }

            // Get table schema to validate columns
            var schema = await GetTableSchemaAsync(tableName);
            var validColumns = schema.Columns.Where(c => !c.IsPrimaryKey && 
                                                        !string.Equals(c.ColumnName, "CreatedDate", StringComparison.OrdinalIgnoreCase))
                                           .Select(c => c.ColumnName)
                                           .ToHashSet(StringComparer.OrdinalIgnoreCase);

            // Filter data to only include valid columns
            var filteredData = data.Where(kvp => validColumns.Contains(kvp.Key))
                                  .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

            if (!filteredData.Any())
            {
                throw new ArgumentException("No valid columns found in the provided data");
            }

            // Add UpdatedDate if the column exists
            if (schema.Columns.Any(c => string.Equals(c.ColumnName, "UpdatedDate", StringComparison.OrdinalIgnoreCase)))
            {
                filteredData["UpdatedDate"] = DateTime.UtcNow;
            }

            // Generate INSERT SQL
            var insertSql = GenerateInsertSql(tableName, filteredData);

            try
            {
                var rowsAffected = await _context.ExecuteRawSqlAsync(insertSql);
                _logger.LogInformation("Successfully inserted {RowsAffected} rows into table: {TableName}", rowsAffected, tableName);
                return rowsAffected;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to insert data into table: {TableName}", tableName);
                throw new InvalidOperationException($"Failed to insert data into table '{tableName}': {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Retrieves data from a dynamic table
        /// </summary>
        /// <param name="tableName">Name of the table</param>
        /// <param name="pageSize">Number of records per page (optional)</param>
        /// <param name="pageNumber">Page number (optional, 1-based)</param>
        /// <returns>List of data records</returns>
        public async Task<List<Dictionary<string, object?>>> GetDataAsync(string tableName, int? pageSize = null, int? pageNumber = null)
        {
            _logger.LogInformation("Getting data from table: {TableName}", tableName);

            if (!await _context.TableExistsAsync(tableName))
            {
                throw new ArgumentException($"Table '{tableName}' does not exist");
            }

            var sql = $"SELECT * FROM [{tableName}]";

            // Add pagination if specified
            if (pageSize.HasValue && pageNumber.HasValue)
            {
                var offset = (pageNumber.Value - 1) * pageSize.Value;
                sql += $" ORDER BY Id OFFSET {offset} ROWS FETCH NEXT {pageSize.Value} ROWS ONLY";
            }
            else
            {
                sql += " ORDER BY Id";
            }

            try
            {
                var result = await _context.ExecuteRawQueryAsync(sql);
                _logger.LogInformation("Successfully retrieved {RecordCount} records from table: {TableName}", result.Count, tableName);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to retrieve data from table: {TableName}", tableName);
                throw new InvalidOperationException($"Failed to retrieve data from table '{tableName}': {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Generates CREATE TABLE SQL statement
        /// </summary>
        /// <param name="request">Table creation request</param>
        /// <returns>CREATE TABLE SQL statement</returns>
        private string GenerateCreateTableSql(CreateTableRequest request)
        {
            var sql = new StringBuilder();
            sql.AppendLine($"CREATE TABLE [{request.TableName}] (");

            // Add default columns
            sql.AppendLine("    [Id] int IDENTITY(1,1) PRIMARY KEY,");
            sql.AppendLine("    [CreatedDate] datetime2 NOT NULL DEFAULT GETUTCDATE(),");
            sql.AppendLine("    [UpdatedDate] datetime2 NULL,");

            // Add custom columns
            foreach (var column in request.Columns)
            {
                sql.AppendLine($"    {GenerateColumnDefinitionSql(column)},");
            }

            // Remove the last comma and close the table definition
            var sqlString = sql.ToString().TrimEnd();
            sqlString = sqlString.TrimEnd(',');
            sqlString += Environment.NewLine + ");";

            return sqlString;
        }

        /// <summary>
        /// Generates SQL column definition
        /// </summary>
        /// <param name="column">Column definition</param>
        /// <returns>SQL column definition</returns>
        private string GenerateColumnDefinitionSql(ColumnDefinition column)
        {
            var sql = new StringBuilder();
            sql.Append($"[{column.Name}] ");

            // Map data types to SQL Server types
            switch (column.Type.ToLower())
            {
                case "string":
                    var maxLength = column.MaxLength ?? 255;
                    sql.Append($"nvarchar({maxLength})");
                    break;
                case "int":
                    sql.Append("int");
                    break;
                case "datetime":
                    sql.Append("datetime2");
                    break;
                case "bool":
                    sql.Append("bit");
                    break;
                case "decimal":
                    var precision = column.Precision ?? 18;
                    var scale = column.Scale ?? 2;
                    sql.Append($"decimal({precision},{scale})");
                    break;
                case "guid":
                    sql.Append("uniqueidentifier");
                    break;
            }

            // Add NULL/NOT NULL constraint
            sql.Append(column.IsRequired ? " NOT NULL" : " NULL");

            // Add default value if specified
            if (column.DefaultValue != null)
            {
                sql.Append($" DEFAULT {FormatDefaultValue(column.DefaultValue, column.Type)}");
            }

            return sql.ToString();
        }

        /// <summary>
        /// Formats default value for SQL
        /// </summary>
        /// <param name="value">Default value</param>
        /// <param name="dataType">Data type</param>
        /// <returns>Formatted default value</returns>
        private string FormatDefaultValue(object value, string dataType)
        {
            switch (dataType.ToLower())
            {
                case "string":
                    return $"'{value.ToString()?.Replace("'", "''")}'";
                case "bool":
                    return Convert.ToBoolean(value) ? "1" : "0";
                case "datetime":
                    if (value.ToString()?.ToUpper() == "GETUTCDATE()")
                        return "GETUTCDATE()";
                    return $"'{value}'";
                case "guid":
                    return $"'{value}'";
                default:
                    return value.ToString() ?? "NULL";
            }
        }

        /// <summary>
        /// Generates INSERT SQL statement
        /// </summary>
        /// <param name="tableName">Table name</param>
        /// <param name="data">Data to insert</param>
        /// <returns>INSERT SQL statement</returns>
        private string GenerateInsertSql(string tableName, Dictionary<string, object?> data)
        {
            var columns = string.Join(", ", data.Keys.Select(k => $"[{k}]"));
            var values = string.Join(", ", data.Values.Select(FormatValueForSql));

            return $"INSERT INTO [{tableName}] ({columns}) VALUES ({values})";
        }

        /// <summary>
        /// Formats a value for SQL insertion
        /// </summary>
        /// <param name="value">Value to format</param>
        /// <returns>Formatted SQL value</returns>
        private string FormatValueForSql(object? value)
        {
            if (value == null)
                return "NULL";

            switch (value)
            {
                case string s:
                    return $"'{s.Replace("'", "''")}'";
                case DateTime dt:
                    return $"'{dt:yyyy-MM-dd HH:mm:ss.fff}'";
                case bool b:
                    return b ? "1" : "0";
                case Guid g:
                    return $"'{g}'";
                default:
                    return value.ToString() ?? "NULL";
            }
        }
    }

    /// <summary>
    /// Interface for dynamic table service
    /// </summary>
    public interface IDynamicTableService
    {
        Task<TableSchema> CreateTableAsync(CreateTableRequest request);
        Task<TableSchema> GetTableSchemaAsync(string tableName);
        Task<List<string>> GetTablesAsync();
        Task<int> InsertDataAsync(string tableName, Dictionary<string, object?> data);
        Task<List<Dictionary<string, object?>>> GetDataAsync(string tableName, int? pageSize = null, int? pageNumber = null);
    }
}
