using Microsoft.EntityFrameworkCore;

namespace DynamicTableAPI.Data
{
    /// <summary>
    /// Database context for dynamic table operations
    /// </summary>
    public class DynamicTableDbContext : DbContext
    {
        public DynamicTableDbContext(DbContextOptions<DynamicTableDbContext> options) : base(options)
        {
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);
            // Additional model configuration can be added here
        }

        /// <summary>
        /// Executes raw SQL command for dynamic table creation
        /// </summary>
        /// <param name="sql">SQL command to execute</param>
        /// <returns>Number of rows affected</returns>
        public async Task<int> ExecuteRawSqlAsync(string sql)
        {
            return await Database.ExecuteSqlRawAsync(sql);
        }

        /// <summary>
        /// Executes raw SQL query and returns results
        /// </summary>
        /// <param name="sql">SQL query to execute</param>
        /// <returns>Query results as list of dictionaries</returns>
        public async Task<List<Dictionary<string, object?>>> ExecuteRawQueryAsync(string sql)
        {
            var results = new List<Dictionary<string, object?>>();
            
            using var command = Database.GetDbConnection().CreateCommand();
            command.CommandText = sql;
            
            await Database.OpenConnectionAsync();
            
            try
            {
                using var reader = await command.ExecuteReaderAsync();
                
                while (await reader.ReadAsync())
                {
                    var row = new Dictionary<string, object?>();
                    for (int i = 0; i < reader.FieldCount; i++)
                    {
                        row[reader.GetName(i)] = reader.IsDBNull(i) ? null : reader.GetValue(i);
                    }
                    results.Add(row);
                }
            }
            finally
            {
                await Database.CloseConnectionAsync();
            }
            
            return results;
        }

        /// <summary>
        /// Gets table schema information
        /// </summary>
        /// <param name="tableName">Name of the table</param>
        /// <returns>Table schema information</returns>
        public async Task<List<Dictionary<string, object?>>> GetTableSchemaAsync(string tableName)
        {
            var sql = @"
                SELECT 
                    COLUMN_NAME as ColumnName,
                    DATA_TYPE as DataType,
                    IS_NULLABLE as IsNullable,
                    CHARACTER_MAXIMUM_LENGTH as MaxLength,
                    NUMERIC_PRECISION as Precision,
                    NUMERIC_SCALE as Scale,
                    COLUMN_DEFAULT as DefaultValue,
                    CASE WHEN pk.COLUMN_NAME IS NOT NULL THEN 1 ELSE 0 END as IsPrimaryKey
                FROM INFORMATION_SCHEMA.COLUMNS c
                LEFT JOIN (
                    SELECT ku.TABLE_NAME, ku.COLUMN_NAME
                    FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
                    INNER JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE ku
                        ON tc.CONSTRAINT_TYPE = 'PRIMARY KEY' 
                        AND tc.CONSTRAINT_NAME = ku.CONSTRAINT_NAME
                ) pk ON c.TABLE_NAME = pk.TABLE_NAME AND c.COLUMN_NAME = pk.COLUMN_NAME
                WHERE c.TABLE_NAME = {0}
                ORDER BY c.ORDINAL_POSITION";

            return await ExecuteRawQueryAsync(sql.Replace("{0}", $"'{tableName}'"));
        }

        /// <summary>
        /// Checks if a table exists in the database
        /// </summary>
        /// <param name="tableName">Name of the table to check</param>
        /// <returns>True if table exists, false otherwise</returns>
        public async Task<bool> TableExistsAsync(string tableName)
        {
            var sql = @"
                SELECT COUNT(*) 
                FROM INFORMATION_SCHEMA.TABLES 
                WHERE TABLE_NAME = {0} AND TABLE_TYPE = 'BASE TABLE'";

            var result = await ExecuteRawQueryAsync(sql.Replace("{0}", $"'{tableName}'"));
            return Convert.ToInt32(result.First().Values.First()) > 0;
        }

        /// <summary>
        /// Gets list of all user tables in the database
        /// </summary>
        /// <returns>List of table names</returns>
        public async Task<List<string>> GetUserTablesAsync()
        {
            var sql = @"
                SELECT TABLE_NAME 
                FROM INFORMATION_SCHEMA.TABLES 
                WHERE TABLE_TYPE = 'BASE TABLE' 
                AND TABLE_SCHEMA = 'dbo'
                ORDER BY TABLE_NAME";

            var result = await ExecuteRawQueryAsync(sql);
            return result.Select(r => r["TABLE_NAME"]?.ToString() ?? "").Where(t => !string.IsNullOrEmpty(t)).ToList();
        }
    }
}
