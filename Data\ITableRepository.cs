namespace DynamicTableAPI.Data
{
    /// <summary>
    /// Repository interface for table operations
    /// </summary>
    public interface ITableRepository
    {
        /// <summary>
        /// Checks if a table exists in the database
        /// </summary>
        /// <param name="tableName">Name of the table to check</param>
        /// <returns>True if table exists, false otherwise</returns>
        Task<bool> TableExistsAsync(string tableName);

        /// <summary>
        /// Gets list of all user tables in the database
        /// </summary>
        /// <returns>List of table names</returns>
        Task<List<string>> GetUserTablesAsync();

        /// <summary>
        /// Gets table schema information
        /// </summary>
        /// <param name="tableName">Name of the table</param>
        /// <returns>Table schema information as list of dictionaries</returns>
        Task<List<Dictionary<string, object?>>> GetTableSchemaAsync(string tableName);

        /// <summary>
        /// Executes a raw SQL command
        /// </summary>
        /// <param name="sql">SQL command to execute</param>
        /// <returns>Number of rows affected</returns>
        Task<int> ExecuteRawSqlAsync(string sql);

        /// <summary>
        /// Executes a raw SQL query and returns results
        /// </summary>
        /// <param name="sql">SQL query to execute</param>
        /// <returns>Query results as list of dictionaries</returns>
        Task<List<Dictionary<string, object?>>> ExecuteRawQueryAsync(string sql);

        /// <summary>
        /// Gets the total count of records in a table
        /// </summary>
        /// <param name="tableName">Name of the table</param>
        /// <returns>Total number of records</returns>
        Task<int> GetTableRecordCountAsync(string tableName);

        /// <summary>
        /// Deletes all data from a table
        /// </summary>
        /// <param name="tableName">Name of the table</param>
        /// <returns>Number of rows deleted</returns>
        Task<int> TruncateTableAsync(string tableName);

        /// <summary>
        /// Drops a table from the database
        /// </summary>
        /// <param name="tableName">Name of the table to drop</param>
        /// <returns>True if successful</returns>
        Task<bool> DropTableAsync(string tableName);
    }
}
