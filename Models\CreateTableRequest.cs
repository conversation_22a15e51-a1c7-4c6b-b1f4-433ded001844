using System.ComponentModel.DataAnnotations;

namespace DynamicTableAPI.Models
{
    /// <summary>
    /// Request model for creating a new dynamic table
    /// </summary>
    public class CreateTableRequest
    {
        /// <summary>
        /// Name of the table to create
        /// </summary>
        [Required(ErrorMessage = "Table name is required")]
        [RegularExpression(@"^[a-zA-Z][a-zA-Z0-9_]*$", ErrorMessage = "Table name must start with a letter and contain only letters, numbers, and underscores")]
        [StringLength(128, MinimumLength = 1, ErrorMessage = "Table name must be between 1 and 128 characters")]
        public string TableName { get; set; } = string.Empty;

        /// <summary>
        /// List of custom columns to add to the table
        /// </summary>
        [Required(ErrorMessage = "At least one column definition is required")]
        [MinLength(1, ErrorMessage = "At least one column definition is required")]
        public List<ColumnDefinition> Columns { get; set; } = new List<ColumnDefinition>();
    }
}
