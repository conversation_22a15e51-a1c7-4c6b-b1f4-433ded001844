<?xml version="1.0"?>
<doc>
    <assembly>
        <name>DynamicTableAPI</name>
    </assembly>
    <members>
        <member name="T:DynamicTableAPI.Controllers.TablesController">
            <summary>
            Controller for managing dynamic tables
            </summary>
        </member>
        <member name="M:DynamicTableAPI.Controllers.TablesController.CreateTable(DynamicTableAPI.Models.CreateTableRequest)">
            <summary>
            Creates a new dynamic table with the specified columns
            </summary>
            <param name="request">Table creation request containing table name and column definitions</param>
            <returns>Created table schema information</returns>
            <response code="201">Table created successfully</response>
            <response code="400">Invalid request data</response>
            <response code="409">Table already exists</response>
            <response code="500">Internal server error</response>
        </member>
        <member name="M:DynamicTableAPI.Controllers.TablesController.GetTables">
            <summary>
            Gets a list of all existing tables
            </summary>
            <returns>List of table names</returns>
            <response code="200">List of tables retrieved successfully</response>
            <response code="500">Internal server error</response>
        </member>
        <member name="M:DynamicTableAPI.Controllers.TablesController.GetTableSchema(System.String)">
            <summary>
            Gets schema information for a specific table
            </summary>
            <param name="tableName">Name of the table</param>
            <returns>Table schema information</returns>
            <response code="200">Table schema retrieved successfully</response>
            <response code="404">Table not found</response>
            <response code="500">Internal server error</response>
        </member>
        <member name="M:DynamicTableAPI.Controllers.TablesController.InsertData(System.String,DynamicTableAPI.Models.InsertDataRequest)">
            <summary>
            Inserts data into a specific table
            </summary>
            <param name="tableName">Name of the table</param>
            <param name="request">Data to insert</param>
            <returns>Number of rows affected</returns>
            <response code="201">Data inserted successfully</response>
            <response code="400">Invalid request data</response>
            <response code="404">Table not found</response>
            <response code="500">Internal server error</response>
        </member>
        <member name="M:DynamicTableAPI.Controllers.TablesController.GetData(System.String,System.Nullable{System.Int32},System.Nullable{System.Int32})">
            <summary>
            Retrieves data from a specific table
            </summary>
            <param name="tableName">Name of the table</param>
            <param name="pageSize">Number of records per page (optional)</param>
            <param name="pageNumber">Page number (optional, 1-based)</param>
            <returns>List of data records</returns>
            <response code="200">Data retrieved successfully</response>
            <response code="404">Table not found</response>
            <response code="500">Internal server error</response>
        </member>
        <member name="T:DynamicTableAPI.Data.DynamicTableDbContext">
            <summary>
            Database context for dynamic table operations
            </summary>
        </member>
        <member name="M:DynamicTableAPI.Data.DynamicTableDbContext.ExecuteRawSqlAsync(System.String)">
            <summary>
            Executes raw SQL command for dynamic table creation
            </summary>
            <param name="sql">SQL command to execute</param>
            <returns>Number of rows affected</returns>
        </member>
        <member name="M:DynamicTableAPI.Data.DynamicTableDbContext.ExecuteRawQueryAsync(System.String)">
            <summary>
            Executes raw SQL query and returns results
            </summary>
            <param name="sql">SQL query to execute</param>
            <returns>Query results as list of dictionaries</returns>
        </member>
        <member name="M:DynamicTableAPI.Data.DynamicTableDbContext.GetTableSchemaAsync(System.String)">
            <summary>
            Gets table schema information
            </summary>
            <param name="tableName">Name of the table</param>
            <returns>Table schema information</returns>
        </member>
        <member name="M:DynamicTableAPI.Data.DynamicTableDbContext.TableExistsAsync(System.String)">
            <summary>
            Checks if a table exists in the database
            </summary>
            <param name="tableName">Name of the table to check</param>
            <returns>True if table exists, false otherwise</returns>
        </member>
        <member name="M:DynamicTableAPI.Data.DynamicTableDbContext.GetUserTablesAsync">
            <summary>
            Gets list of all user tables in the database
            </summary>
            <returns>List of table names</returns>
        </member>
        <member name="T:DynamicTableAPI.Data.ITableRepository">
            <summary>
            Repository interface for table operations
            </summary>
        </member>
        <member name="M:DynamicTableAPI.Data.ITableRepository.TableExistsAsync(System.String)">
            <summary>
            Checks if a table exists in the database
            </summary>
            <param name="tableName">Name of the table to check</param>
            <returns>True if table exists, false otherwise</returns>
        </member>
        <member name="M:DynamicTableAPI.Data.ITableRepository.GetUserTablesAsync">
            <summary>
            Gets list of all user tables in the database
            </summary>
            <returns>List of table names</returns>
        </member>
        <member name="M:DynamicTableAPI.Data.ITableRepository.GetTableSchemaAsync(System.String)">
            <summary>
            Gets table schema information
            </summary>
            <param name="tableName">Name of the table</param>
            <returns>Table schema information as list of dictionaries</returns>
        </member>
        <member name="M:DynamicTableAPI.Data.ITableRepository.ExecuteRawSqlAsync(System.String)">
            <summary>
            Executes a raw SQL command
            </summary>
            <param name="sql">SQL command to execute</param>
            <returns>Number of rows affected</returns>
        </member>
        <member name="M:DynamicTableAPI.Data.ITableRepository.ExecuteRawQueryAsync(System.String)">
            <summary>
            Executes a raw SQL query and returns results
            </summary>
            <param name="sql">SQL query to execute</param>
            <returns>Query results as list of dictionaries</returns>
        </member>
        <member name="M:DynamicTableAPI.Data.ITableRepository.GetTableRecordCountAsync(System.String)">
            <summary>
            Gets the total count of records in a table
            </summary>
            <param name="tableName">Name of the table</param>
            <returns>Total number of records</returns>
        </member>
        <member name="M:DynamicTableAPI.Data.ITableRepository.TruncateTableAsync(System.String)">
            <summary>
            Deletes all data from a table
            </summary>
            <param name="tableName">Name of the table</param>
            <returns>Number of rows deleted</returns>
        </member>
        <member name="M:DynamicTableAPI.Data.ITableRepository.DropTableAsync(System.String)">
            <summary>
            Drops a table from the database
            </summary>
            <param name="tableName">Name of the table to drop</param>
            <returns>True if successful</returns>
        </member>
        <member name="T:DynamicTableAPI.Data.TableRepository">
            <summary>
            Repository implementation for table operations
            </summary>
        </member>
        <member name="M:DynamicTableAPI.Data.TableRepository.TableExistsAsync(System.String)">
            <summary>
            Checks if a table exists in the database
            </summary>
            <param name="tableName">Name of the table to check</param>
            <returns>True if table exists, false otherwise</returns>
        </member>
        <member name="M:DynamicTableAPI.Data.TableRepository.GetUserTablesAsync">
            <summary>
            Gets list of all user tables in the database
            </summary>
            <returns>List of table names</returns>
        </member>
        <member name="M:DynamicTableAPI.Data.TableRepository.GetTableSchemaAsync(System.String)">
            <summary>
            Gets table schema information
            </summary>
            <param name="tableName">Name of the table</param>
            <returns>Table schema information as list of dictionaries</returns>
        </member>
        <member name="M:DynamicTableAPI.Data.TableRepository.ExecuteRawSqlAsync(System.String)">
            <summary>
            Executes a raw SQL command
            </summary>
            <param name="sql">SQL command to execute</param>
            <returns>Number of rows affected</returns>
        </member>
        <member name="M:DynamicTableAPI.Data.TableRepository.ExecuteRawQueryAsync(System.String)">
            <summary>
            Executes a raw SQL query and returns results
            </summary>
            <param name="sql">SQL query to execute</param>
            <returns>Query results as list of dictionaries</returns>
        </member>
        <member name="M:DynamicTableAPI.Data.TableRepository.GetTableRecordCountAsync(System.String)">
            <summary>
            Gets the total count of records in a table
            </summary>
            <param name="tableName">Name of the table</param>
            <returns>Total number of records</returns>
        </member>
        <member name="M:DynamicTableAPI.Data.TableRepository.TruncateTableAsync(System.String)">
            <summary>
            Deletes all data from a table
            </summary>
            <param name="tableName">Name of the table</param>
            <returns>Number of rows deleted</returns>
        </member>
        <member name="M:DynamicTableAPI.Data.TableRepository.DropTableAsync(System.String)">
            <summary>
            Drops a table from the database
            </summary>
            <param name="tableName">Name of the table to drop</param>
            <returns>True if successful</returns>
        </member>
        <member name="T:DynamicTableAPI.Middleware.GlobalExceptionMiddleware">
            <summary>
            Global exception handling middleware
            </summary>
        </member>
        <member name="T:DynamicTableAPI.Middleware.GlobalExceptionMiddlewareExtensions">
            <summary>
            Extension method to register the global exception middleware
            </summary>
        </member>
        <member name="T:DynamicTableAPI.Middleware.SwaggerSchemaExampleFilter">
            <summary>
            Swagger schema filter to add examples to API documentation
            </summary>
        </member>
        <member name="T:DynamicTableAPI.Models.ApiResponse`1">
            <summary>
            Generic API response wrapper
            </summary>
            <typeparam name="T">Type of the response data</typeparam>
        </member>
        <member name="P:DynamicTableAPI.Models.ApiResponse`1.Success">
            <summary>
            Indicates whether the operation was successful
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.ApiResponse`1.Data">
            <summary>
            Response data
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.ApiResponse`1.Message">
            <summary>
            Error message if the operation failed
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.ApiResponse`1.Errors">
            <summary>
            List of validation errors
            </summary>
        </member>
        <member name="M:DynamicTableAPI.Models.ApiResponse`1.SuccessResponse(`0,System.String)">
            <summary>
            Creates a successful response
            </summary>
            <param name="data">Response data</param>
            <param name="message">Optional success message</param>
            <returns>Successful API response</returns>
        </member>
        <member name="M:DynamicTableAPI.Models.ApiResponse`1.ErrorResponse(System.String,System.Collections.Generic.List{System.String})">
            <summary>
            Creates an error response
            </summary>
            <param name="message">Error message</param>
            <param name="errors">List of validation errors</param>
            <returns>Error API response</returns>
        </member>
        <member name="T:DynamicTableAPI.Models.ApiResponse">
            <summary>
            Non-generic API response for operations that don't return data
            </summary>
        </member>
        <member name="M:DynamicTableAPI.Models.ApiResponse.SuccessResponse(System.String)">
            <summary>
            Creates a successful response without data
            </summary>
            <param name="message">Success message</param>
            <returns>Successful API response</returns>
        </member>
        <member name="T:DynamicTableAPI.Models.ColumnDefinition">
            <summary>
            Represents a column definition for dynamic table creation
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.ColumnDefinition.Name">
            <summary>
            Name of the column
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.ColumnDefinition.Type">
            <summary>
            Data type of the column (string, int, datetime, bool, decimal, guid)
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.ColumnDefinition.MaxLength">
            <summary>
            Maximum length for string columns (default: 255)
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.ColumnDefinition.Precision">
            <summary>
            Precision for decimal columns
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.ColumnDefinition.Scale">
            <summary>
            Scale for decimal columns
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.ColumnDefinition.IsRequired">
            <summary>
            Whether the column is required (NOT NULL)
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.ColumnDefinition.DefaultValue">
            <summary>
            Default value for the column
            </summary>
        </member>
        <member name="T:DynamicTableAPI.Models.CreateTableRequest">
            <summary>
            Request model for creating a new dynamic table
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.CreateTableRequest.TableName">
            <summary>
            Name of the table to create
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.CreateTableRequest.Columns">
            <summary>
            List of custom columns to add to the table
            </summary>
        </member>
        <member name="T:DynamicTableAPI.Models.InsertDataRequest">
            <summary>
            Request model for inserting data into a dynamic table
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.InsertDataRequest.Data">
            <summary>
            Data to insert as key-value pairs where key is column name and value is the data
            </summary>
        </member>
        <member name="T:DynamicTableAPI.Models.BulkInsertDataRequest">
            <summary>
            Request model for inserting multiple rows of data
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.BulkInsertDataRequest.Data">
            <summary>
            List of data rows to insert
            </summary>
        </member>
        <member name="T:DynamicTableAPI.Models.TableSchema">
            <summary>
            Represents the schema information of a table
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.TableSchema.TableName">
            <summary>
            Name of the table
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.TableSchema.Columns">
            <summary>
            List of columns in the table
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.TableSchema.CreatedDate">
            <summary>
            Date and time when the table was created
            </summary>
        </member>
        <member name="T:DynamicTableAPI.Models.ColumnSchema">
            <summary>
            Represents the schema information of a column
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.ColumnSchema.ColumnName">
            <summary>
            Name of the column
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.ColumnSchema.DataType">
            <summary>
            Data type of the column
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.ColumnSchema.IsNullable">
            <summary>
            Whether the column allows null values
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.ColumnSchema.MaxLength">
            <summary>
            Maximum length for string columns
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.ColumnSchema.Precision">
            <summary>
            Precision for numeric columns
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.ColumnSchema.Scale">
            <summary>
            Scale for decimal columns
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.ColumnSchema.DefaultValue">
            <summary>
            Default value for the column
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.ColumnSchema.IsPrimaryKey">
            <summary>
            Whether the column is a primary key
            </summary>
        </member>
        <member name="T:DynamicTableAPI.Services.DynamicTableService">
            <summary>
            Service for managing dynamic table operations
            </summary>
        </member>
        <member name="M:DynamicTableAPI.Services.DynamicTableService.CreateTableAsync(DynamicTableAPI.Models.CreateTableRequest)">
            <summary>
            Creates a new dynamic table with the specified columns
            </summary>
            <param name="request">Table creation request</param>
            <returns>Created table schema</returns>
        </member>
        <member name="M:DynamicTableAPI.Services.DynamicTableService.GetTableSchemaAsync(System.String)">
            <summary>
            Gets the schema information for a specific table
            </summary>
            <param name="tableName">Name of the table</param>
            <returns>Table schema information</returns>
        </member>
        <member name="M:DynamicTableAPI.Services.DynamicTableService.GetTablesAsync">
            <summary>
            Gets a list of all user tables in the database
            </summary>
            <returns>List of table names</returns>
        </member>
        <member name="M:DynamicTableAPI.Services.DynamicTableService.InsertDataAsync(System.String,System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            Inserts data into a dynamic table
            </summary>
            <param name="tableName">Name of the table</param>
            <param name="data">Data to insert</param>
            <returns>Number of rows affected</returns>
        </member>
        <member name="M:DynamicTableAPI.Services.DynamicTableService.GetDataAsync(System.String,System.Nullable{System.Int32},System.Nullable{System.Int32})">
            <summary>
            Retrieves data from a dynamic table
            </summary>
            <param name="tableName">Name of the table</param>
            <param name="pageSize">Number of records per page (optional)</param>
            <param name="pageNumber">Page number (optional, 1-based)</param>
            <returns>List of data records</returns>
        </member>
        <member name="M:DynamicTableAPI.Services.DynamicTableService.GenerateCreateTableSql(DynamicTableAPI.Models.CreateTableRequest)">
            <summary>
            Generates CREATE TABLE SQL statement
            </summary>
            <param name="request">Table creation request</param>
            <returns>CREATE TABLE SQL statement</returns>
        </member>
        <member name="M:DynamicTableAPI.Services.DynamicTableService.GenerateColumnDefinitionSql(DynamicTableAPI.Models.ColumnDefinition)">
            <summary>
            Generates SQL column definition
            </summary>
            <param name="column">Column definition</param>
            <returns>SQL column definition</returns>
        </member>
        <member name="M:DynamicTableAPI.Services.DynamicTableService.FormatDefaultValue(System.Object,System.String)">
            <summary>
            Formats default value for SQL
            </summary>
            <param name="value">Default value</param>
            <param name="dataType">Data type</param>
            <returns>Formatted default value</returns>
        </member>
        <member name="M:DynamicTableAPI.Services.DynamicTableService.GenerateInsertSql(System.String,System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            Generates INSERT SQL statement
            </summary>
            <param name="tableName">Table name</param>
            <param name="data">Data to insert</param>
            <returns>INSERT SQL statement</returns>
        </member>
        <member name="M:DynamicTableAPI.Services.DynamicTableService.FormatValueForSql(System.Object)">
            <summary>
            Formats a value for SQL insertion
            </summary>
            <param name="value">Value to format</param>
            <returns>Formatted SQL value</returns>
        </member>
        <member name="T:DynamicTableAPI.Services.IDynamicTableService">
            <summary>
            Interface for dynamic table service
            </summary>
        </member>
        <member name="T:DynamicTableAPI.Validators.TableValidationService">
            <summary>
            Service for validating table and column definitions
            </summary>
        </member>
        <member name="M:DynamicTableAPI.Validators.TableValidationService.ValidateCreateTableRequest(DynamicTableAPI.Models.CreateTableRequest)">
            <summary>
            Validates a table creation request
            </summary>
            <param name="request">The table creation request to validate</param>
            <returns>Validation result with any errors</returns>
        </member>
        <member name="M:DynamicTableAPI.Validators.TableValidationService.ValidateTableName(System.String)">
            <summary>
            Validates a table name
            </summary>
            <param name="tableName">The table name to validate</param>
            <returns>Validation result</returns>
        </member>
        <member name="M:DynamicTableAPI.Validators.TableValidationService.ValidateColumnDefinition(DynamicTableAPI.Models.ColumnDefinition,System.Int32)">
            <summary>
            Validates a column definition
            </summary>
            <param name="column">The column definition to validate</param>
            <param name="index">The index of the column in the request (for error reporting)</param>
            <returns>Validation result</returns>
        </member>
        <member name="T:DynamicTableAPI.Validators.ITableValidationService">
            <summary>
            Interface for table validation service
            </summary>
        </member>
        <member name="T:DynamicTableAPI.Validators.ValidationResult">
            <summary>
            Represents the result of a validation operation
            </summary>
        </member>
    </members>
</doc>
