{"createTableExamples": {"products": {"description": "Create a Products table with various data types", "request": {"tableName": "Products", "columns": [{"name": "Name", "type": "string", "maxLength": 100, "isRequired": true}, {"name": "Price", "type": "decimal", "precision": 10, "scale": 2, "isRequired": true}, {"name": "IsActive", "type": "bool", "defaultValue": true}, {"name": "Description", "type": "string", "maxLength": 500, "isRequired": false}, {"name": "CategoryId", "type": "int", "isRequired": false}, {"name": "ProductId", "type": "guid", "isRequired": true}]}}, "customers": {"description": "Create a Customers table", "request": {"tableName": "Customers", "columns": [{"name": "FirstName", "type": "string", "maxLength": 50, "isRequired": true}, {"name": "LastName", "type": "string", "maxLength": 50, "isRequired": true}, {"name": "Email", "type": "string", "maxLength": 255, "isRequired": true}, {"name": "DateOfBirth", "type": "datetime", "isRequired": false}, {"name": "IsVip", "type": "bool", "defaultValue": false}, {"name": "CreditLimit", "type": "decimal", "precision": 12, "scale": 2, "defaultValue": 0.0}]}}, "orders": {"description": "Create an Orders table", "request": {"tableName": "Orders", "columns": [{"name": "OrderNumber", "type": "string", "maxLength": 20, "isRequired": true}, {"name": "CustomerId", "type": "int", "isRequired": true}, {"name": "OrderDate", "type": "datetime", "isRequired": true}, {"name": "TotalAmount", "type": "decimal", "precision": 15, "scale": 2, "isRequired": true}, {"name": "Status", "type": "string", "maxLength": 20, "defaultValue": "Pending"}, {"name": "IsRushOrder", "type": "bool", "defaultValue": false}]}}}, "insertDataExamples": {"products": {"description": "Insert sample product data", "requests": [{"data": {"Name": "Laptop Computer", "Price": 999.99, "IsActive": true, "Description": "High-performance laptop for business use", "CategoryId": 1, "ProductId": "123e4567-e89b-12d3-a456-426614174000"}}, {"data": {"Name": "Wireless Mouse", "Price": 29.99, "IsActive": true, "Description": "Ergonomic wireless mouse with USB receiver", "CategoryId": 2, "ProductId": "987fcdeb-51a2-43d1-9f12-123456789abc"}}, {"data": {"Name": "Office Chair", "Price": 199.5, "IsActive": false, "Description": "Comfortable office chair with lumbar support", "CategoryId": 3, "ProductId": "456789ab-cdef-1234-5678-90abcdef1234"}}]}, "customers": {"description": "Insert sample customer data", "requests": [{"data": {"FirstName": "<PERSON>", "LastName": "<PERSON><PERSON>", "Email": "<EMAIL>", "DateOfBirth": "1985-03-15T00:00:00", "IsVip": true, "CreditLimit": 5000.0}}, {"data": {"FirstName": "<PERSON>", "LastName": "<PERSON>", "Email": "<EMAIL>", "DateOfBirth": "1990-07-22T00:00:00", "IsVip": false, "CreditLimit": 2500.0}}]}, "orders": {"description": "Insert sample order data", "requests": [{"data": {"OrderNumber": "ORD-2024-001", "CustomerId": 1, "OrderDate": "2024-01-15T10:30:00", "TotalAmount": 1029.98, "Status": "Processing", "IsRushOrder": false}}, {"data": {"OrderNumber": "ORD-2024-002", "CustomerId": 2, "OrderDate": "2024-01-16T14:45:00", "TotalAmount": 229.49, "Status": "Shipped", "IsRushOrder": true}}]}}, "testScenarios": {"validationTests": {"description": "Test cases for validation scenarios", "invalidTableName": {"tableName": "123InvalidName", "columns": [{"name": "TestColumn", "type": "string"}]}, "reservedWordTableName": {"tableName": "SELECT", "columns": [{"name": "TestColumn", "type": "string"}]}, "invalidColumnType": {"tableName": "TestTable", "columns": [{"name": "TestColumn", "type": "invalidtype"}]}, "duplicateColumnNames": {"tableName": "TestTable", "columns": [{"name": "TestColumn", "type": "string"}, {"name": "TestColumn", "type": "int"}]}}}}