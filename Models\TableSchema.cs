namespace DynamicTableAPI.Models
{
    /// <summary>
    /// Represents the schema information of a table
    /// </summary>
    public class TableSchema
    {
        /// <summary>
        /// Name of the table
        /// </summary>
        public string TableName { get; set; } = string.Empty;

        /// <summary>
        /// List of columns in the table
        /// </summary>
        public List<ColumnSchema> Columns { get; set; } = new List<ColumnSchema>();

        /// <summary>
        /// Date and time when the table was created
        /// </summary>
        public DateTime CreatedDate { get; set; }
    }

    /// <summary>
    /// Represents the schema information of a column
    /// </summary>
    public class ColumnSchema
    {
        /// <summary>
        /// Name of the column
        /// </summary>
        public string ColumnName { get; set; } = string.Empty;

        /// <summary>
        /// Data type of the column
        /// </summary>
        public string DataType { get; set; } = string.Empty;

        /// <summary>
        /// Whether the column allows null values
        /// </summary>
        public bool IsNullable { get; set; }

        /// <summary>
        /// Maximum length for string columns
        /// </summary>
        public int? MaxLength { get; set; }

        /// <summary>
        /// Precision for numeric columns
        /// </summary>
        public int? Precision { get; set; }

        /// <summary>
        /// Scale for decimal columns
        /// </summary>
        public int? Scale { get; set; }

        /// <summary>
        /// Default value for the column
        /// </summary>
        public string? DefaultValue { get; set; }

        /// <summary>
        /// Whether the column is a primary key
        /// </summary>
        public bool IsPrimaryKey { get; set; }
    }
}
