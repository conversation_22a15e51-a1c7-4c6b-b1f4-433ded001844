# Dynamic Table API

A RESTful Web API built with ASP.NET Core that supports dynamic database table creation through REST endpoints.

## Features

- **Dynamic Table Creation**: Create database tables dynamically through API calls
- **Multiple Data Types**: Support for string, int, datetime, bool, decimal, and guid data types
- **Automatic System Columns**: Every table includes Id (Primary Key), CreatedDate, and UpdatedDate columns
- **Data Operations**: Insert and retrieve data from dynamically created tables
- **Validation**: Comprehensive validation for table names, column names, and data types
- **Error Handling**: Global exception handling with appropriate HTTP status codes
- **Swagger Documentation**: Complete API documentation with examples
- **SQL Server LocalDB**: Uses Entity Framework Core with SQL Server LocalDB

## Prerequisites

- .NET 6.0 or later
- SQL Server LocalDB (included with Visual Studio)
- Visual Studio 2022 or VS Code

## Getting Started

### 1. Clone and Setup

```bash
git clone <repository-url>
cd WebAPINoGraphQL
```

### 2. Restore Dependencies

```bash
dotnet restore
```

### 3. Run the Application

```bash
dotnet run
```

The API will be available at:
- HTTP: `http://localhost:5000`
- HTTPS: `https://localhost:5001`
- Swagger UI: `https://localhost:5001/swagger`

## API Endpoints

### Table Management

#### Create Table
- **POST** `/api/tables/create`
- Creates a new dynamic table with specified columns
- **Request Body**: JSON with table name and column definitions
- **Response**: 201 Created with table schema

#### List Tables
- **GET** `/api/tables`
- Returns list of all existing tables
- **Response**: 200 OK with array of table names

#### Get Table Schema
- **GET** `/api/tables/{tableName}`
- Returns schema information for a specific table
- **Response**: 200 OK with table schema details

### Data Operations

#### Insert Data
- **POST** `/api/tables/{tableName}/data`
- Inserts data into a specific table
- **Request Body**: JSON with data as key-value pairs
- **Response**: 201 Created with number of rows affected

#### Retrieve Data
- **GET** `/api/tables/{tableName}/data`
- Retrieves data from a specific table
- **Query Parameters**: 
  - `pageSize` (optional): Number of records per page
  - `pageNumber` (optional): Page number (1-based)
- **Response**: 200 OK with array of data records

## Supported Data Types

| Type | SQL Server Type | Parameters | Example |
|------|----------------|------------|---------|
| `string` | nvarchar(n) | maxLength (default: 255) | `"type": "string", "maxLength": 100` |
| `int` | int | - | `"type": "int"` |
| `datetime` | datetime2 | - | `"type": "datetime"` |
| `bool` | bit | - | `"type": "bool"` |
| `decimal` | decimal(p,s) | precision, scale | `"type": "decimal", "precision": 10, "scale": 2` |
| `guid` | uniqueidentifier | - | `"type": "guid"` |

## Sample Requests

### Create a Products Table

```json
{
  "tableName": "Products",
  "columns": [
    {
      "name": "Name",
      "type": "string",
      "maxLength": 100,
      "isRequired": true
    },
    {
      "name": "Price",
      "type": "decimal",
      "precision": 10,
      "scale": 2,
      "isRequired": true
    },
    {
      "name": "IsActive",
      "type": "bool",
      "defaultValue": true
    },
    {
      "name": "Description",
      "type": "string",
      "maxLength": 500,
      "isRequired": false
    }
  ]
}
```

### Insert Product Data

```json
{
  "data": {
    "Name": "Laptop Computer",
    "Price": 999.99,
    "IsActive": true,
    "Description": "High-performance laptop for business use"
  }
}
```

## Database Configuration

The application uses SQL Server LocalDB with the following connection string:

```
Data Source=(localdb)\MSSQLLocalDB;Initial Catalog=Infodat;Integrated Security=True;Persist Security Info=False;Pooling=False;Multiple Active Result Sets=False;Encrypt=True;Trust Server Certificate=False;Command Timeout=0
```

The database is automatically created when the application starts.

## Validation Rules

### Table Names
- Must start with a letter
- Can contain letters, numbers, and underscores
- Cannot exceed 128 characters
- Cannot be SQL Server reserved words

### Column Names
- Must start with a letter
- Can contain letters, numbers, and underscores
- Cannot exceed 128 characters
- Cannot be SQL Server reserved words
- Cannot be system column names (Id, CreatedDate, UpdatedDate)

### Data Types
- Must be one of the supported types
- String columns: maxLength between 1 and 4000
- Decimal columns: precision between 1 and 38, scale ≤ precision

## Error Handling

The API returns appropriate HTTP status codes:

- **200 OK**: Successful retrieval
- **201 Created**: Successful creation
- **400 Bad Request**: Invalid input or validation errors
- **404 Not Found**: Table or resource not found
- **409 Conflict**: Table already exists
- **500 Internal Server Error**: Unexpected server errors

## Testing

### Using Swagger UI

1. Navigate to `https://localhost:5001/swagger`
2. Explore the API endpoints with interactive documentation
3. Use the "Try it out" feature to test endpoints
4. View example requests and responses

### Using Sample Data

The `SampleRequests.json` file contains comprehensive examples for:
- Creating different types of tables
- Inserting various data types
- Testing validation scenarios

### Manual Testing with curl

```bash
# Create a table
curl -X POST "https://localhost:5001/api/tables/create" \
  -H "Content-Type: application/json" \
  -d @sample-create-table.json

# List tables
curl -X GET "https://localhost:5001/api/tables"

# Insert data
curl -X POST "https://localhost:5001/api/tables/Products/data" \
  -H "Content-Type: application/json" \
  -d @sample-insert-data.json
```

## Architecture

The application follows clean architecture principles:

- **Controllers**: Handle HTTP requests and responses
- **Services**: Business logic and table operations
- **Data**: Entity Framework context and repositories
- **Models**: DTOs and request/response models
- **Validators**: Input validation logic
- **Middleware**: Global exception handling

## Logging

The application includes comprehensive logging:
- Request/response logging
- Error logging with stack traces
- SQL command logging (in debug mode)
- Performance monitoring

## Security Considerations

- Input validation and sanitization
- SQL injection prevention through parameterized queries
- Error message sanitization
- CORS configuration for cross-origin requests

## Troubleshooting

### Common Issues

1. **Database Connection**: Ensure SQL Server LocalDB is installed
2. **Port Conflicts**: Check if ports 5000/5001 are available
3. **Permissions**: Ensure write permissions for LocalDB files

### Logs

Check the console output for detailed error messages and debugging information.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License.
