using System.ComponentModel.DataAnnotations;

namespace DynamicTableAPI.Models
{
    /// <summary>
    /// Represents a column definition for dynamic table creation
    /// </summary>
    public class ColumnDefinition
    {
        /// <summary>
        /// Name of the column
        /// </summary>
        [Required(ErrorMessage = "Column name is required")]
        [RegularExpression(@"^[a-zA-Z][a-zA-Z0-9_]*$", ErrorMessage = "Column name must start with a letter and contain only letters, numbers, and underscores")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Data type of the column (string, int, datetime, bool, decimal, guid)
        /// </summary>
        [Required(ErrorMessage = "Column type is required")]
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// Maximum length for string columns (default: 255)
        /// </summary>
        public int? MaxLength { get; set; }

        /// <summary>
        /// Precision for decimal columns
        /// </summary>
        public int? Precision { get; set; }

        /// <summary>
        /// Scale for decimal columns
        /// </summary>
        public int? Scale { get; set; }

        /// <summary>
        /// Whether the column is required (NOT NULL)
        /// </summary>
        public bool IsRequired { get; set; } = false;

        /// <summary>
        /// Default value for the column
        /// </summary>
        public object? DefaultValue { get; set; }
    }
}
